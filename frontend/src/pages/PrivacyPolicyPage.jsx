import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

import FloatingCTA from '../components/FloatingCTA';

const PrivacyPolicyPage = () => {
  // Set page title and meta description
  useEffect(() => {
    document.title = 'Privacy Policy | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Privacy Policy for VerifiedOnward. Learn how we collect, use, and protect your information when using our embassy-approved flight reservation service.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
    };
  }, []);

  return (
    <div className="min-h-screen">


      {/* Premium Floating CTA */}
      <FloatingCTA />

      {/* £20,000+ Luxury Header Section */}
      <section className="relative min-h-screen flex items-center gradient-luxury-hero overflow-hidden">
        {/* Luxury Background System */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/10 via-blue-500/6 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-brand-400/15 to-brand-600/8 rounded-full blur-3xl animate-luxury-float"></div>
        <div className="absolute bottom-20 right-10 w-[32rem] h-[32rem] bg-gradient-to-br from-accent-400/15 to-accent-600/8 rounded-full blur-3xl animate-luxury-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-br from-blue-400/12 to-purple-400/8 rounded-full blur-2xl animate-luxury-float" style={{animationDelay: '4s'}}></div>

        <div className="container-modern text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Premium Trust Badge */}
            <div className="inline-flex items-center bg-accent-100 text-accent-700 px-6 py-3 rounded-full text-sm font-semibold mb-8 shadow-success-glow">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              Your Privacy is Our Priority • GDPR Compliant
            </div>

            <h1 className="text-luxury-display mb-8 animate-luxury-shimmer">
              Privacy{' '}
              <span className="bg-gradient-to-r from-brand-600 via-blue-600 to-accent-600 bg-clip-text text-transparent">
                Policy
              </span>
            </h1>

            <p className="text-luxury-subtitle leading-relaxed mb-12 max-w-4xl mx-auto">
              At VerifiedOnward, your privacy is our top priority.
              <strong className="text-brand-700"> We're committed to protecting your personal information with bank-level security.</strong>
            </p>

            {/* Premium Stats */}
            <div className="flex flex-wrap justify-center items-center gap-8 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-600 mb-1">256-bit</div>
                <div className="text-sm text-neutral-600">SSL Encryption</div>
              </div>
              <div className="w-px h-8 bg-neutral-300"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-accent-600 mb-1">GDPR</div>
                <div className="text-sm text-neutral-600">Compliant</div>
              </div>
              <div className="w-px h-8 bg-neutral-300"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-1">0</div>
                <div className="text-sm text-neutral-600">Data Breaches</div>
              </div>
            </div>

            {/* Premium CTA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <Link to="/" className="premium-button-large inline-flex items-center group">
                <svg className="w-6 h-6 mr-3 group-hover:animate-bounce-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Start Your Secure Reservation
                <svg className="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Premium Content Section */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>
        <div className="absolute top-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-72 h-72 bg-brand-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 8A6 6 0 006 8v2.5a2 2 0 01-2 2v2a2 2 0 002 2h12a2 2 0 002-2v-2a2 2 0 01-2-2V8z" clipRule="evenodd" />
              </svg>
              Transparent Privacy Practices
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              How We
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent block md:inline md:ml-2">
                Protect Your Data
              </span>
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="premium-card relative overflow-hidden"
          >
            {/* Premium background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>

            <div className="relative z-10 prose prose-lg max-w-none">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {[
                  {
                    number: "1",
                    title: "Information We Collect",
                    icon: "📋",
                    content: [
                      "Personal Information: Only essential details like name, email, and passenger information",
                      "Payment Details: Processed securely through Stripe/PayPal - never stored on our servers"
                    ]
                  },
                  {
                    number: "2",
                    title: "How We Use Your Information",
                    icon: "🔧",
                    content: [
                      "Generate and deliver your flight reservation instantly via email",
                      "Communicate about your order when necessary",
                      "Improve our service experience"
                    ]
                  },
                  {
                    number: "3",
                    title: "Data Protection",
                    icon: "🛡️",
                    content: [
                      "256-bit SSL encryption for all data transmission",
                      "Industry-standard security protocols",
                      "Zero data sharing with third parties"
                    ]
                  },
                  {
                    number: "4",
                    title: "Your Rights",
                    icon: "⚖️",
                    content: [
                      "Request data deletion at any time",
                      "Full transparency about data usage",
                      "GDPR compliant practices"
                    ]
                  }
                ].map((section, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-2xl p-6 shadow-soft border border-neutral-200 hover:shadow-medium transition-all duration-300 group"
                  >
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full flex items-center justify-center text-white font-bold mr-4 shadow-glow group-hover:scale-110 transition-transform">
                        {section.number}
                      </div>
                      <div>
                        <div className="text-2xl mb-1">{section.icon}</div>
                        <h3 className="text-xl font-bold text-neutral-900 group-hover:text-brand-600 transition-colors">
                          {section.title}
                        </h3>
                      </div>
                    </div>
                    <ul className="space-y-2">
                      {section.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start text-neutral-700">
                          <div className="w-2 h-2 bg-accent-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                          <span className="leading-relaxed">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                ))}
              </div>

              {/* Additional Sections */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8"
              >
                <div className="bg-gradient-to-r from-neutral-50 to-white rounded-2xl p-6 border border-neutral-200">
                  <h3 className="text-xl font-bold text-neutral-900 mb-4 flex items-center">
                    <span className="text-2xl mr-3">🍪</span>
                    Cookies Policy
                  </h3>
                  <p className="text-neutral-700 leading-relaxed">
                    We use essential cookies to enhance site functionality and collect limited analytics.
                    By using our site, you agree to our cookie usage for improving your experience.
                  </p>
                </div>

                <div className="bg-gradient-to-r from-neutral-50 to-white rounded-2xl p-6 border border-neutral-200">
                  <h3 className="text-xl font-bold text-neutral-900 mb-4 flex items-center">
                    <span className="text-2xl mr-3">🔄</span>
                    Policy Updates
                  </h3>
                  <p className="text-neutral-700 leading-relaxed">
                    This policy may be updated occasionally to reflect service improvements.
                    Continued use of our site implies acceptance of any revised policy.
                  </p>
                </div>
              </motion.div>

              {/* Contact Section */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="mt-12 text-center bg-gradient-to-r from-brand-50 to-accent-50 rounded-2xl p-8 border border-brand-200"
              >
                <h3 className="text-2xl font-bold text-neutral-900 mb-4">
                  Questions About Your Privacy?
                </h3>
                <p className="text-neutral-700 mb-6 max-w-2xl mx-auto">
                  We're committed to transparency. If you have any questions about how we handle your data
                  or want to exercise your privacy rights, we're here to help.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="premium-button inline-flex items-center group"
                >
                  <svg className="w-5 h-5 mr-2 group-hover:animate-bounce-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  Contact Privacy Team
                </a>
              </motion.div>
            </div>

            {/* Premium shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>
          </motion.div>
        </div>
      </section>


    </div>
  );
};

export default PrivacyPolicyPage;
