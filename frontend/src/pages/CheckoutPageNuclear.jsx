import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast, ToastContainer } from 'react-toastify';
import {
  CreditCardIcon,
  ArrowLeftIcon,
  ShieldCheckIcon,
  UserIcon,
  PaperAirplaneIcon,
  ExclamationTriangleIcon,
  PencilSquareIcon,
  CheckCircleIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import { getCheckoutData } from '../utils/sessionStorageHelper';
import { useBooking } from '../context/BookingContext';
import { paymentAPI } from '../services/api';
import { generateReservationCode } from '../utils/reservationCodeGenerator';

const BookingSummaryPage = () => {
  console.log('✅ BookingSummaryPage: Professional checkout page loading...');
  console.log('🔧 DEBUGGING: CheckoutPageNuclear with FIXED pricing and edit functionality loaded!');

  const navigate = useNavigate();
  const location = useLocation();
  const {
    selectedFlight,
    selectedOutboundFlight,
    selectedReturnFlight,
    passengers,
    email,
    tripType
  } = useBooking();

  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingStripe, setIsProcessingStripe] = useState(false);
  const [isProcessingPayPal, setIsProcessingPayPal] = useState(false);
  const [bookingData, setBookingData] = useState(null);

  // Clear any demo/test data on component mount
  useEffect(() => {
    // Clear potentially corrupted demo data
    const keysToCheck = [
      'nuclearCheckoutData',
      'checkoutBackupData',
      'bookingSelectedFlight',
      'bookingSelectedOutboundFlight',
      'bookingSelectedReturnFlight'
    ];

    keysToCheck.forEach(key => {
      try {
        const data = localStorage.getItem(key);
        if (data) {
          const parsed = JSON.parse(data);
          // Remove demo/test data
          if (parsed && (
            (parsed.airline?.name === 'British Airways' && parsed.flightNumber === 'BA 117') ||
            (parsed.flight?.number === 'BA 117') ||
            (parsed.selectedFlight?.airline?.name === 'British Airways' && parsed.selectedFlight?.flightNumber === 'BA 117') ||
            (parsed.id && (parsed.id.includes('demo') || parsed.id.includes('test')))
          )) {
            console.log(`🧹 Clearing demo data from ${key}`);
            localStorage.removeItem(key);
          }
        }
      } catch (e) {
        // Ignore parsing errors
      }
    });
  }, []);

  // Load booking data from multiple sources
  useEffect(() => {
    console.log('✅ BookingSummaryPage: Loading booking data...');
    console.log('✅ BookingSummaryPage: Context data:', { selectedFlight, selectedOutboundFlight, selectedReturnFlight, passengers, email, tripType });
    console.log('✅ BookingSummaryPage: location.state:', location.state);
    console.log('✅ BookingSummaryPage: sessionStorage data:', getCheckoutData());

    try {
      let data = null;

      // Priority 1: Use React Context data if available (REAL FLIGHT DATA)
      if ((selectedFlight || selectedOutboundFlight) && passengers.length > 0 && email) {
        console.log('✅ BookingSummaryPage: Using REAL context data from flight selection');
        data = {
          selectedFlight,
          selectedOutboundFlight,
          selectedReturnFlight,
          passengers,
          email,
          tripType
        };
      }

      // Priority 2: Check React Router location.state (from navigation)
      if (!data && location.state) {
        console.log('✅ BookingSummaryPage: Found location.state data:', location.state);
        data = { ...location.state };

        // Normalize the data format for consistency - map SearchResultsPage format to expected format
        if (data.flight && !data.selectedFlight) {
          console.log('✅ BookingSummaryPage: Mapping flight -> selectedFlight');
          data.selectedFlight = data.flight;
        }
        if (data.outboundFlight && !data.selectedOutboundFlight) {
          console.log('✅ BookingSummaryPage: Mapping outboundFlight -> selectedOutboundFlight');
          data.selectedOutboundFlight = data.outboundFlight;
        }
        if (data.returnFlight && !data.selectedReturnFlight) {
          console.log('✅ BookingSummaryPage: Mapping returnFlight -> selectedReturnFlight');
          data.selectedReturnFlight = data.returnFlight;
        }

        console.log('✅ BookingSummaryPage: Normalized location.state data:', data);
      }

      // Priority 3: Check sessionStorage (for page refreshes)
      if (!data) {
        data = getCheckoutData();
        if (data) {
          console.log('✅ BookingSummaryPage: Found sessionStorage data');
        }
      }

      // Priority 4: If still no data, try to create minimal fallback data for testing
      if (!data) {
        console.log('⚠️ BookingSummaryPage: No booking data found - checking for minimal fallback');

        // Check if we can create minimal data from URL params or other sources
        const urlParams = new URLSearchParams(window.location.search);
        const hasTestMode = urlParams.get('test') === 'true';

        if (hasTestMode) {
          console.log('🧪 BookingSummaryPage: Creating test data for development');
          data = {
            tripType: 'oneWay',
            passengers: [{ firstName: 'Test', lastName: 'User' }],
            email: '<EMAIL>',
            flight: {
              id: 'test-flight-1',
              airline: { name: 'Test Airlines', code: 'TA' },
              flightNumber: 'TA 123',
              flight: {
                departure: { time: '2024-01-01T10:00:00Z', airport: 'JFK' },
                arrival: { time: '2024-01-01T14:00:00Z', airport: 'LAX' }
              },
              price: { total: 4.99 }
            }
          };
        } else {
          console.log('❌ BookingSummaryPage: No booking data found - redirecting to search');
          setError('No booking data found. Please start a new flight search.');
          setTimeout(() => {
            navigate('/');
          }, 2000);
          return;
        }
      }

      // Save data to sessionStorage for reload-proofing
      if (data && (location.state || selectedFlight || selectedOutboundFlight)) {
        try {
          sessionStorage.setItem('checkoutBookingData', JSON.stringify(data));
          console.log('✅ BookingSummaryPage: Backup data saved to sessionStorage');
        } catch (error) {
          console.warn('⚠️ BookingSummaryPage: Failed to save backup data:', error);
        }
      }

      console.log('✅ BookingSummaryPage: Successfully loaded REAL booking data:', data);
      setBookingData(data);

    } catch (error) {
      console.error('❌ BookingSummaryPage: Error loading booking data:', error);
      setError('Error loading booking data. Please try again.');
    }

    // Complete loading after a short delay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [location.state, selectedFlight, selectedOutboundFlight, selectedReturnFlight, passengers, email, tripType, navigate]);

  // Calculate total price based on trip type (NOT passenger count)
  const calculateTotalPrice = () => {
    if (!bookingData) return '4.99';
    const tripType = bookingData.tripType || 'oneWay';

    // Price is per FLIGHT, not per passenger
    // One-way: $4.99, Return: $4.99 + $4.99 = $9.98
    if (tripType === 'return') {
      return '9.98'; // Outbound + Return flight
    } else {
      return '4.99'; // One-way flight only
    }
  };

  // Handle edit details with preserved state - Navigate directly to passenger details
  const handleEditDetails = () => {
    try {
      // Save current checkout progress to sessionStorage
      const checkoutProgress = {
        step: 'payment',
        timestamp: Date.now(),
        returnUrl: '/checkout',
        bookingData: bookingData,
        totalPrice: calculateTotalPrice()
      };

      sessionStorage.setItem('checkoutProgress', JSON.stringify(checkoutProgress));
      console.log('✅ CheckoutPage: Progress saved for edit flow');

      // Navigate back to homepage with edit mode state and direct passenger details access
      navigate('/', {
        state: {
          editMode: true,
          showPassengerDetailsDirectly: true, // New flag to show passenger details immediately
          preservedData: {
            // Flight search data
            from: bookingData.departureAirport || bookingData.from,
            to: bookingData.arrivalAirport || bookingData.to,
            departureDate: bookingData.departureDate,
            returnDate: bookingData.returnDate,
            tripType: bookingData.tripType,

            // Passenger data
            passengers: bookingData.passengers,
            email: bookingData.email,

            // Flight selections
            selectedFlight: bookingData.selectedFlight,
            selectedOutboundFlight: bookingData.selectedOutboundFlight,
            selectedReturnFlight: bookingData.selectedReturnFlight
          },
          returnToCheckout: true,
          message: 'Edit your passenger details below. Your progress has been saved.'
        }
      });

      // Show user feedback
      toast.info('Redirecting to passenger details...', {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

    } catch (error) {
      console.error('❌ CheckoutPage: Error in edit flow:', error);
      toast.error('Unable to edit details. Please try again.', {
        position: "top-center",
        autoClose: 3000,
      });
    }
  };

  // Get flight data for display - handle multiple data formats
  const getDepartureFlightData = () => {
    if (!bookingData) {
      console.log('🔍 getDepartureFlightData: No bookingData available');
      return null;
    }

    console.log('🔍 getDepartureFlightData: Checking bookingData:', bookingData);

    // Handle different data formats from various sources
    // Priority order: context data -> navigation state data -> fallbacks
    const flightData = bookingData.selectedFlight ||           // From BookingContext (one-way)
                      bookingData.selectedOutboundFlight ||    // From BookingContext (return trip)
                      bookingData.flight ||                    // From SearchResultsPage navigation (one-way)
                      bookingData.outboundFlight;              // From SearchResultsPage navigation (return trip)

    console.log('🔍 getDepartureFlightData: Found flight data:', flightData);

    // Basic validation - ensure we have some flight data structure
    if (!flightData || typeof flightData !== 'object') {
      console.log('🚫 getDepartureFlightData: No valid flight data structure');
      return null;
    }

    // More lenient validation - only reject if it's clearly invalid
    // Allow demo data for testing purposes, but log it
    if (flightData.airline?.name === 'British Airways' && flightData.flightNumber === 'BA 117') {
      console.log('⚠️ getDepartureFlightData: Using demo flight data (BA 117)');
    }

    return flightData;
  };

  const getReturnFlightData = () => {
    if (!bookingData) return null;

    console.log('🔍 getReturnFlightData: Checking bookingData:', bookingData);

    // Handle different data formats from various sources
    const returnData = bookingData.selectedReturnFlight ||     // From BookingContext
                      bookingData.returnFlight;                // From SearchResultsPage navigation

    console.log('🔍 getReturnFlightData: Found return flight data:', returnData);
    return returnData;
  };

  // Handle Stripe payment
  const handleStripePayment = async () => {
    // Prevent double-clicking and ensure mutual exclusivity
    if (isProcessingStripe || isProcessingPayPal) return;

    setIsProcessingStripe(true);
    setError(null);

    try {
      console.log('💳 Processing Stripe payment...');

      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate booking reference
      const bookingRef = generateReservationCode();

      // Show success toast (fixed: removed duplicate emoji)
      toast.success('Payment successful! Your verified flight reservation is ready to download.');

      // Navigate to success page
      setTimeout(() => {
        navigate('/success', {
          state: {
            bookingReference: bookingRef,
            paymentData: { success: true, paymentId: `stripe_${Date.now()}`, method: 'stripe' },
            selectedFlight: getDepartureFlightData(),
            returnFlight: getReturnFlightData(),
            passengers: passengersData,
            email: emailData,
            tripType: tripTypeData,
            totalPrice: calculateTotalPrice()
          }
        });
      }, 1500);

    } catch (err) {
      console.error('❌ Stripe payment error:', err);
      setError('Payment failed. Please try again.');
      setIsProcessingStripe(false);
    }
  };

  // Handle PayPal payment
  const handlePayPalPayment = async () => {
    // Prevent double-clicking and ensure mutual exclusivity
    if (isProcessingStripe || isProcessingPayPal) return;

    setIsProcessingPayPal(true);
    setError(null);

    try {
      console.log('💰 Processing PayPal payment...');

      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate booking reference
      const bookingRef = generateReservationCode();

      // Show success toast (fixed: removed duplicate emoji)
      toast.success('Payment successful! Your verified flight reservation is ready to download.');

      // Navigate to success page
      setTimeout(() => {
        navigate('/success', {
          state: {
            bookingReference: bookingRef,
            paymentData: { success: true, paymentId: `paypal_${Date.now()}`, method: 'paypal' },
            selectedFlight: getDepartureFlightData(),
            returnFlight: getReturnFlightData(),
            passengers: passengersData,
            email: emailData,
            tripType: tripTypeData,
            totalPrice: calculateTotalPrice()
          }
        });
      }, 1500);

    } catch (err) {
      console.error('❌ PayPal payment error:', err);
      setError('Payment failed. Please try again.');
      setIsProcessingPayPal(false);
    }
  };



  // Unified Booking Overview Component
  const BookingOverview = ({ departureFlightData, returnFlightData, passengersData, emailData, tripTypeData }) => {
    // Helper function to format airport codes and names
    const formatAirport = (flight, type) => {
      if (!flight) return 'N/A';

      // Handle nested flight.departure/arrival structure (from API)
      const flightData = flight.flight || flight;
      const airportData = type === 'departure' ? flightData.departure : flightData.arrival;

      // Handle direct departure/arrival on flight object
      const directData = type === 'departure' ? flight.departure : flight.arrival;

      // Handle legacy from/to format
      const fallbackData = type === 'departure' ? flight.from : flight.to;

      // Priority: nested structure -> direct structure -> fallback
      const targetData = airportData || directData;

      if (targetData?.iataCode) {
        return targetData.iataCode;
      } else if (targetData?.airport) {
        // Extract IATA code from airport string like "London Heathrow (LHR)" or just return airport code
        const match = targetData.airport.match(/\(([A-Z]{3})\)/);
        return match ? match[1] : targetData.airport;
      } else if (fallbackData) {
        const match = fallbackData.match(/\(([A-Z]{3})\)/);
        return match ? match[1] : fallbackData;
      }
      return 'N/A';
    };

    // Helper function to format time
    const formatTime = (flight, type) => {
      if (!flight) return 'N/A';

      // Handle nested flight.departure/arrival structure (from API)
      const flightData = flight.flight || flight;
      const timeData = type === 'departure' ? flightData.departure?.time : flightData.arrival?.time;

      // Handle direct departure/arrival on flight object
      const directTimeData = type === 'departure' ? flight.departure?.time : flight.arrival?.time;

      // Handle legacy format
      const fallbackTime = type === 'departure' ? flight.departureTime : flight.arrivalTime;

      const time = timeData || directTimeData || fallbackTime;
      if (!time) return 'N/A';

      // If it's already formatted time like "10:30 AM", return as is
      if (time.includes('AM') || time.includes('PM')) {
        return time;
      }

      // Try to parse and format the time
      try {
        const date = new Date(time);
        return date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });
      } catch {
        return time;
      }
    };

    return (
      <div className="card-aviation">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-black text-brand-700 flex items-center">
            <div className="w-8 h-8 bg-green-100 rounded-xl flex items-center justify-center mr-3">
              <CheckCircleIcon className="h-5 w-5 text-green-600" />
            </div>
            Booking Overview
          </h2>
          <button
            onClick={handleEditDetails}
            className="flex items-center text-brand-600 hover:text-brand-700 text-sm font-bold transition-all duration-300 px-4 py-2 rounded-xl hover:bg-brand-50 border border-brand-200 hover:border-brand-300"
          >
            <PencilSquareIcon className="h-5 w-5 mr-2" />
            <span>Edit Details</span>
            <div className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
              Progress Saved
            </div>
          </button>
        </div>

        {/* Premium Passengers Section */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-brand-700 mb-4 flex items-center">
            <div className="w-8 h-8 bg-brand-100 rounded-xl flex items-center justify-center mr-3">
              <UserIcon className="h-5 w-5 text-brand-600" />
            </div>
            Passengers
          </h3>
          <div className="space-y-3">
            {passengersData.map((passenger, index) => (
              <div key={index} className="flex items-center p-4 bg-brand-50 rounded-2xl border border-brand-200">
                <div className="w-10 h-10 bg-brand-500 rounded-xl flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-sm">{index + 1}</span>
                </div>
                <span className="font-bold text-brand-700 text-lg">
                  {passenger.firstName} {passenger.lastName}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Premium Aviation Flights Section */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-brand-700 mb-6 flex items-center">
            <div className="w-8 h-8 bg-brand-100 rounded-xl flex items-center justify-center mr-3">
              <PaperAirplaneIcon className="h-5 w-5 text-brand-600" />
            </div>
            Flight Details
          </h3>

          <div className="space-y-6">
            {/* Premium Departure Flight */}
            {departureFlightData && (
              <div className="bg-gradient-to-r from-brand-50 to-brand-100/50 rounded-2xl p-6 border-2 border-brand-200 shadow-aviation">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    {departureFlightData.airline?.logo && (
                      <img
                        src={departureFlightData.airline.logo}
                        alt={departureFlightData.airline.name}
                        className="h-8 w-8 mr-3 rounded-lg shadow-soft"
                        onError={(e) => { e.target.style.display = 'none'; }}
                      />
                    )}
                    <div>
                      <span className="font-black text-brand-700 text-lg">
                        {departureFlightData.airline?.name || departureFlightData.airline || 'Airline'} {departureFlightData.flightNumber || departureFlightData.flight?.number || departureFlightData.flight_number || 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center px-3 py-1 bg-brand-500 text-white rounded-full text-sm font-bold">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                    </svg>
                    Departure
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="font-bold text-brand-800 text-xl mb-2">
                      {formatAirport(departureFlightData, 'departure')} → {formatAirport(departureFlightData, 'arrival')}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-brand-700 text-lg">{formatTime(departureFlightData, 'departure')} → {formatTime(departureFlightData, 'arrival')}</div>
                    <div className="text-sm text-brand-600 font-medium">Duration: {departureFlightData.duration || departureFlightData.flight?.duration || 'N/A'}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Premium Return Flight */}
            {returnFlightData && (
              <div className="bg-gradient-to-r from-accent-50 to-accent-100/50 rounded-2xl p-6 border-2 border-accent-200 shadow-aviation">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    {returnFlightData.airline?.logo && (
                      <img
                        src={returnFlightData.airline.logo}
                        alt={returnFlightData.airline.name}
                        className="h-8 w-8 mr-3 rounded-lg shadow-soft"
                        onError={(e) => { e.target.style.display = 'none'; }}
                      />
                    )}
                    <div>
                      <span className="font-black text-accent-700 text-lg">
                        {returnFlightData.airline?.name || returnFlightData.airline || 'Airline'} {returnFlightData.flightNumber || returnFlightData.flight?.number || returnFlightData.flight_number || 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center px-3 py-1 bg-accent-500 text-white rounded-full text-sm font-bold">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                    Return
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="font-bold text-accent-800 text-xl mb-2">
                      {formatAirport(returnFlightData, 'departure')} → {formatAirport(returnFlightData, 'arrival')}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-accent-700 text-lg">{formatTime(returnFlightData, 'departure')} → {formatTime(returnFlightData, 'arrival')}</div>
                    <div className="text-sm text-accent-600 font-medium">Duration: {returnFlightData.duration || returnFlightData.flight?.duration || 'N/A'}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Instant Download Note */}
        <div className="bg-green-50 rounded-lg p-3 border border-green-200">
          <p className="text-sm text-green-800 flex items-center">
            <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            ✅ Once your payment is successful, you'll be able to instantly download your ticket.
          </p>
        </div>
      </div>
    );
  };

  // Payment Summary Component
  const PaymentSummary = ({ totalPrice, passengerCount, onStripePayment, onPayPalPayment, isProcessingStripe, isProcessingPayPal }) => {
    // Calculate flight count based on trip type
    const tripType = tripTypeData || 'oneWay';
    const flightCount = tripType === 'return' ? 2 : 1;
    const flightLabel = tripType === 'return' ? '2 flights (Return)' : '1 flight (One-way)';

    return (
      <div className="space-y-8">
        {/* Clear & Honest Pricing Display */}
        <div className="bg-gradient-to-r from-brand-50 to-brand-100/50 rounded-2xl p-6 border-2 border-brand-200">
          <div className="text-center mb-6">
            <div className="inline-flex items-center bg-brand-500 text-white px-4 py-2 rounded-full text-sm font-bold mb-3">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Professional Flight Reservation Service
            </div>
            <div className="text-5xl font-black text-brand-600 mb-2">
              ${totalPrice}
            </div>
            <div className="text-lg text-brand-700 font-semibold">
              {tripType === 'return' ? 'Complete Return Journey' : 'One-Way Flight Reservation'}
            </div>
          </div>

          <div className="bg-white/80 rounded-xl p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center text-brand-700">
                <div className="w-8 h-8 bg-brand-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-semibold">Embassy-Approved Format</span>
              </div>
              <div className="flex items-center text-brand-700">
                <div className="w-8 h-8 bg-brand-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-semibold">60-Second Delivery</span>
              </div>
              <div className="flex items-center text-brand-700">
                <div className="w-8 h-8 bg-brand-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-semibold">Verifiable Booking Reference</span>
              </div>
              <div className="flex items-center text-brand-700">
                <div className="w-8 h-8 bg-brand-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-semibold">Trusted by 195+ Embassies</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
            <div className="flex items-center justify-center text-green-700">
              <svg className="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="font-bold">
                {tripType === 'return'
                  ? 'Includes both outbound and return flight reservations'
                  : 'Professional one-way flight reservation document'
                }
              </span>
            </div>
          </div>
        </div>

        {/* 60-Second Delivery Promise */}
        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 mb-6 border border-green-200">
          <div className="flex items-center justify-center">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
              <svg className="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <div className="font-bold text-green-800">60-Second Delivery Promise</div>
              <div className="text-sm text-green-700">Your flight reservation will be ready in 60 seconds after payment</div>
            </div>
          </div>
        </div>

        {/* Premium Aviation Payment Buttons */}
        <div className="space-y-4">
          <motion.button
            onClick={onStripePayment}
            disabled={isProcessingStripe || isProcessingPayPal}
            className="w-full bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white py-4 px-6 rounded-2xl font-black text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 active:scale-95 touch-manipulation min-h-[56px]"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isProcessingStripe ? (
              <>
                <div className="animate-spin rounded-full h-6 w-6 border-b-3 border-white mr-3"></div>
                <span className="font-black">Processing Payment...</span>
              </>
            ) : (
              <>
                <CreditCardIcon className="h-6 w-6 mr-3" />
                <span className="font-black">Complete Payment - ${totalPrice}</span>
                <div className="bg-white/20 rounded-lg px-2 py-1 text-sm ml-3">60s</div>
              </>
            )}
          </motion.button>

          <motion.button
            onClick={onPayPalPayment}
            disabled={isProcessingStripe || isProcessingPayPal}
            className="w-full bg-white border-2 border-brand-500 text-brand-600 hover:bg-brand-50 py-4 px-6 rounded-2xl font-black text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-aviation hover:shadow-aviation-hover transform hover:scale-105 active:scale-95 touch-manipulation min-h-[56px]"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isProcessingPayPal ? (
              <>
                <div className="animate-spin rounded-full h-6 w-6 border-b-3 border-brand-500 mr-3"></div>
                <span className="font-black">Processing Payment...</span>
              </>
            ) : (
              <>
                <div className="bg-brand-500 rounded-xl px-3 py-1 mr-3 shadow-soft">
                  <span className="text-white font-black text-sm">PayPal</span>
                </div>
                <span className="font-black">Or pay with PayPal</span>
              </>
            )}
          </motion.button>
        </div>

        {/* Consolidated Trust & Security Section */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200">
          <div className="flex items-center justify-center mb-4">
            <LockClosedIcon className="h-6 w-6 text-green-600 mr-2" />
            <span className="text-lg font-bold text-green-700">Your payment is 100% secure</span>
          </div>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                <svg className="h-6 w-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-sm font-semibold text-green-700">Bank-Level SSL</span>
            </div>

            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              </div>
              <span className="text-sm font-semibold text-green-700">Embassy Approved</span>
            </div>

            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                <svg className="h-6 w-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-sm font-semibold text-green-700">60s Download</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Show premium loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-brand-50/30 to-accent-50/20 flex items-center justify-center relative overflow-hidden">
        {/* Premium Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-transparent to-accent-500/5"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-brand-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>

        <div className="text-center relative z-10">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200, damping: 20 }}
            className="w-20 h-20 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-luxury relative overflow-hidden"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 border-4 border-white border-t-transparent rounded-full"
            ></motion.div>

            {/* Floating particles */}
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white rounded-full opacity-60"
                animate={{
                  y: [-10, -30, -10],
                  x: [0, Math.random() * 20 - 10, 0],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.5,
                }}
                style={{
                  left: `${30 + i * 20}%`,
                  top: '50%',
                }}
              />
            ))}
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h2 className="text-2xl font-bold text-neutral-900 mb-2">
              Preparing Your Checkout
            </h2>
            <p className="text-neutral-600 text-lg">
              Setting up your secure payment environment...
            </p>

            <div className="flex justify-center items-center mt-6 space-x-4">
              <div className="trust-badge">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                SSL Secured
              </div>
              <div className="trust-badge">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Embassy Approved
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  // Show error state if no booking data
  if (error || !bookingData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-brand-50/30 to-accent-50/20 py-8 relative overflow-hidden">
        {/* Premium Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 via-transparent to-accent-500/5"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-brand-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>

        <div className="max-w-2xl mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="premium-card text-center relative overflow-hidden"
          >
            {/* Premium background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-orange-500/5"></div>

            <div className="relative z-10">
              {/* Premium Error Icon */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ type: "spring", stiffness: 200, damping: 20 }}
                className="w-20 h-20 bg-gradient-to-r from-red-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-luxury"
              >
                <ExclamationTriangleIcon className="h-10 w-10 text-white" />
              </motion.div>

              <h1 className="text-3xl font-bold text-neutral-900 mb-4">
                Oops! No Flight Selected
              </h1>
              <p className="text-xl text-neutral-600 mb-8">
                {error || "We couldn't find your flight data. This usually happens when:"}
              </p>

              {!error && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                  {[
                    { icon: '🔗', text: 'Direct page access' },
                    { icon: '⏰', text: 'Session expired' },
                    { icon: '⚡', text: 'Data corrupted' }
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 + index * 0.1 }}
                      className="bg-neutral-50 rounded-2xl p-4 border border-neutral-200"
                    >
                      <div className="text-2xl mb-2">{item.icon}</div>
                      <p className="text-sm text-neutral-600 font-medium">{item.text}</p>
                    </motion.div>
                  ))}
                </div>
              )}

              {/* Debug information in development */}
              {process.env.NODE_ENV === 'development' && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="bg-neutral-100 rounded-2xl p-6 mb-8 text-left"
                >
                  <h3 className="font-bold text-neutral-800 mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    Debug Information
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="font-medium">Context data:</span>
                      <span className={selectedFlight || selectedOutboundFlight ? 'text-accent-600' : 'text-red-600'}>
                        {selectedFlight || selectedOutboundFlight ? '✓ Present' : '✗ Missing'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Location state:</span>
                      <span className={location.state ? 'text-accent-600' : 'text-red-600'}>
                        {location.state ? '✓ Present' : '✗ Missing'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">SessionStorage:</span>
                      <span className={getCheckoutData() ? 'text-accent-600' : 'text-red-600'}>
                        {getCheckoutData() ? '✓ Present' : '✗ Missing'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Error:</span>
                      <span className="text-red-600">{error || 'None'}</span>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Premium Action Buttons */}
              <div className="space-y-4">
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  onClick={() => navigate('/')}
                  className="premium-button-large w-full group"
                >
                  <svg className="w-6 h-6 mr-3 group-hover:animate-bounce-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Start New Flight Search
                  <svg className="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </motion.button>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  onClick={() => navigate(-1)}
                  className="w-full border-2 border-neutral-300 text-neutral-700 py-4 px-6 rounded-2xl font-semibold hover:bg-neutral-50 hover:border-brand-300 hover:text-brand-600 transition-all duration-300 group"
                >
                  <ArrowLeftIcon className="h-5 w-5 mr-2 inline group-hover:animate-bounce-subtle" />
                  Back to Previous Page
                </motion.button>

                {/* Development buttons */}
                {process.env.NODE_ENV === 'development' && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8 }}
                    className="space-y-2 pt-4 border-t border-neutral-200"
                  >
                    <button
                      onClick={() => navigate('/checkout-test')}
                      className="w-full bg-orange-500 text-white py-2 px-4 rounded-xl hover:bg-orange-600 transition-colors text-sm font-medium"
                    >
                      🧪 Go to Test Page
                    </button>
                    <button
                      onClick={() => window.location.href = '/checkout?test=true'}
                      className="w-full bg-purple-500 text-white py-2 px-4 rounded-xl hover:bg-purple-600 transition-colors text-sm font-medium"
                    >
                      🔧 Load Test Data
                    </button>
                  </motion.div>
                )}
              </div>
            </div>

            {/* Premium shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>
          </motion.div>
        </div>
      </div>
    );
  }

  // Get flight and passenger data for display
  const departureFlightData = getDepartureFlightData();
  const returnFlightData = getReturnFlightData();
  const passengersData = bookingData?.passengers || [];
  const emailData = bookingData?.email || '';
  const tripTypeData = bookingData?.tripType || 'oneWay';
  const totalPrice = calculateTotalPrice();

  // Debug logging
  console.log('BookingSummaryPage: Render data:', {
    bookingData,
    passengersData,
    emailData,
    departureFlightData,
    returnFlightData
  });

  // CRITICAL CHECK: If no real flight data, show error
  if (!departureFlightData) {
    console.log('🚫 BookingSummaryPage: No valid flight data found - showing error');
    console.log('🚫 BookingSummaryPage: Booking data structure:', bookingData);
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-8">
            <h1 className="text-2xl font-bold text-red-800 mb-4">
              ❌ No Flight Selected
            </h1>
            <p className="text-red-700 mb-6">
              No valid flight data was found. This usually happens when:
            </p>
            <ul className="text-left text-red-700 mb-6 space-y-2">
              <li>• You navigated directly to checkout without selecting a flight</li>
              <li>• Your session expired</li>
              <li>• The flight data was corrupted</li>
            </ul>

            {/* Debug information in development */}
            {process.env.NODE_ENV === 'development' && bookingData && (
              <div className="bg-white rounded-lg p-4 mb-6 text-left text-sm">
                <h3 className="font-semibold mb-2 text-gray-900">Available Data Keys:</h3>
                <div className="text-gray-600 space-y-1">
                  {Object.keys(bookingData).map(key => (
                    <p key={key}><strong>{key}:</strong> {typeof bookingData[key]} {bookingData[key] ? '✓' : '✗'}</p>
                  ))}
                </div>
              </div>
            )}

            <div className="space-y-4">
              <button
                onClick={() => navigate('/')}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold"
              >
                🔍 Start New Flight Search
              </button>
              <br />
              {process.env.NODE_ENV === 'development' && (
                <>
                  <button
                    onClick={() => navigate('/checkout-test')}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 text-sm mr-2"
                  >
                    🧪 Go to Test Page
                  </button>
                  <button
                    onClick={() => window.location.href = '/checkout?test=true'}
                    className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 text-sm"
                  >
                    🔧 Load Test Data
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen aviation-gradient-hero relative overflow-hidden">
      {/* Premium Aviation Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-500/8 via-transparent to-accent-500/8"></div>
      <div className="absolute top-20 left-10 w-80 h-80 bg-brand-400/15 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-400/15 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-brand-300/10 to-accent-300/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

      {/* Subtle Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>

      <div className="relative z-10 min-h-screen py-8">
        <div className="container-modern">
          {/* Premium Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            {/* Premium Aviation Trust Badge */}
            <div className="inline-flex items-center bg-white/90 backdrop-blur-md text-brand-700 px-8 py-4 rounded-2xl text-base font-bold mb-8 shadow-aviation border border-brand-200">
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
              </div>
              Secure Checkout • Bank-Level SSL • Instant Download
            </div>

            <h1 className="text-2xl md:text-3xl font-bold text-brand-800 mb-4 leading-tight">
              Complete Your
              <span className="bg-gradient-to-r from-accent-600 to-accent-500 bg-clip-text text-transparent">
                ${totalPrice} Reservation
              </span>
            </h1>

            <div className="max-w-4xl mx-auto mb-12">
              <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-6">
                You're just one step away from your professional, embassy-approved flight reservation.
                <strong className="text-brand-800"> Review your details and complete your secure payment.</strong>
              </p>
            </div>



            {/* Premium Aviation Progress Indicator */}
            <div className="max-w-2xl mx-auto">
              <div className="flex items-center justify-between text-base font-bold text-brand-700 mb-4">
                <span>Flight Selected</span>
                <span>Secure Payment</span>
                <span>Instant Download</span>
              </div>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-500 rounded-2xl flex items-center justify-center shadow-aviation">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1 h-2 bg-gradient-to-r from-green-500 to-brand-500 mx-4 rounded-full"></div>
                <div className="w-12 h-12 bg-gradient-to-r from-brand-500 to-brand-600 rounded-2xl flex items-center justify-center shadow-aviation animate-pulse">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                  </svg>
                </div>
                <div className="flex-1 h-2 bg-neutral-300 mx-4 rounded-full"></div>
                <div className="w-12 h-12 bg-neutral-300 rounded-2xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-neutral-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Premium Main Layout */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Left Column - Premium Booking Overview */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <BookingOverview
                  departureFlightData={departureFlightData}
                  returnFlightData={returnFlightData}
                  passengersData={passengersData}
                  emailData={emailData}
                  tripTypeData={tripTypeData}
                />
              </motion.div>
            </div>

            {/* Right Column - Premium Payment Summary */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="card-aviation sticky top-8 relative overflow-hidden"
              >
                {/* Premium background effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>

                <div className="relative z-10">
                  {/* Premium Aviation Payment Header */}
                  <div className="flex items-center justify-between mb-10">
                    <h2 className="text-3xl font-black text-brand-700 flex items-center">
                      <div className="w-12 h-12 bg-gradient-to-br from-brand-500 to-brand-600 rounded-2xl flex items-center justify-center mr-4 shadow-aviation">
                        <CreditCardIcon className="h-7 w-7 text-white" />
                      </div>
                      Secure Payment
                    </h2>
                    <div className="flex items-center bg-green-100 text-green-700 px-4 py-2 rounded-xl text-sm font-bold border border-green-200">
                      <LockClosedIcon className="h-5 w-5 mr-2" />
                      Bank-Level SSL
                    </div>
                  </div>

                  <PaymentSummary
                    totalPrice={totalPrice}
                    passengerCount={passengersData.length}
                    onStripePayment={handleStripePayment}
                    onPayPalPayment={handlePayPalPayment}
                    isProcessingStripe={isProcessingStripe}
                    isProcessingPayPal={isProcessingPayPal}
                  />

                  {/* Premium Error Display */}
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="mt-6 bg-gradient-to-r from-red-50 to-red-100 border-2 border-red-200 rounded-2xl p-4 shadow-soft"
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <p className="text-red-800 font-medium">{error}</p>
                      </div>
                    </motion.div>
                  )}

                  {/* Progress Commitment Section */}
                  <div className="text-center py-4 border-t border-neutral-200 mt-6">
                    <div className="flex items-center justify-center space-x-2 text-sm text-neutral-600 mb-2">
                      <CheckCircleIcon className="h-4 w-4 text-green-500" />
                      <span>Step 2 of 3 - Almost there!</span>
                    </div>
                    <p className="text-xs text-neutral-500">
                      Need help? <a href="mailto:<EMAIL>" className="text-brand-600 hover:underline">Contact support</a>
                    </p>
                  </div>

                  {/* Premium Trust Indicators */}
                  <div className="mt-6 pt-6 border-t border-neutral-200">
                    <div className="flex items-center justify-center space-x-6 text-xs text-neutral-500">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1 text-accent-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                        256-bit SSL
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1 text-accent-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Professional Quality
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1 text-accent-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        Instant Download
                      </div>
                    </div>
                  </div>
                </div>

                {/* Premium shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default BookingSummaryPage;
