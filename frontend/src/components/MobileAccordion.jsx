import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';

const MobileAccordion = ({ title, children, icon }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-white/10">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between py-4 text-left focus:outline-none"
      >
        <div className="flex items-center space-x-3">
          {icon && <span className="text-lg">{icon}</span>}
          <h4 className="text-lg font-bold bg-gradient-to-r from-brand-300 to-accent-300 bg-clip-text text-transparent">
            {title}
          </h4>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
          className="text-white"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </motion.div>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="pb-4">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const MobileFooterAccordion = () => {
  return (
    <div className="lg:hidden">
      <MobileAccordion title="Quick Links" icon="⚡">
        <ul className="space-y-3">
          {[
            { to: "/how-it-works", label: "How It Works", icon: "⚡" },
            { to: "/use-cases", label: "Use Cases", icon: "💼" },
            { to: "/faq", label: "FAQ", icon: "❓" },
            { to: "/blog", label: "Blog / Resources", icon: "📚" },
            { to: "/privacy-policy", label: "Privacy Policy", icon: "🔒" },
            { to: "/terms-of-service", label: "Terms of Service", icon: "📋" }
          ].map((link, index) => (
            <li key={link.to}>
              <Link
                to={link.to}
                className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-lg px-3 py-2 transition-all duration-300 group text-sm font-medium"
              >
                <span className="text-sm group-hover:scale-110 transition-transform duration-200">{link.icon}</span>
                <span className="group-hover:translate-x-1 transition-transform duration-200">{link.label}</span>
              </Link>
            </li>
          ))}
        </ul>
      </MobileAccordion>

      <MobileAccordion title="Support" icon="📞">
        <ul className="space-y-3">
          <li>
            <Link
              to="/contact"
              className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-lg px-3 py-2 transition-all duration-300 group text-sm font-medium"
            >
              <span className="text-sm group-hover:scale-110 transition-transform duration-200">📞</span>
              <span className="group-hover:translate-x-1 transition-transform duration-200">Contact Us</span>
            </Link>
          </li>
          <li>
            <Link
              to="/refund-policy"
              className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-lg px-3 py-2 transition-all duration-300 group text-sm font-medium"
            >
              <span className="text-sm group-hover:scale-110 transition-transform duration-200">📋</span>
              <span className="group-hover:translate-x-1 transition-transform duration-200">Refund Policy</span>
            </Link>
          </li>
        </ul>
      </MobileAccordion>
    </div>
  );
};

export default MobileFooterAccordion;
