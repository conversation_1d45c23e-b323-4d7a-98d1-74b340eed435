import React from 'react';
import { motion } from 'framer-motion';

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "UK Visa Applicant",
      location: "London, UK",
      text: "Perfect for my visa application. Embassy accepted it without any questions! Saved me $400 compared to buying a real ticket.",
      color: "#3B82F6",
      avatar: "SJ",
      timeAgo: "2 days ago",
      visaType: "Tourist Visa",
      approved: true
    },
    {
      name: "<PERSON>",
      role: "Schengen Visa Applicant",
      location: "Toronto, Canada",
      text: "Quick, reliable service for my visa application. Got approved in 3 days! The document looked exactly like a real booking.",
      color: "#10B981",
      avatar: "MC",
      timeAgo: "1 week ago",
      visaType: "Business Visa",
      approved: true
    },
    {
      name: "<PERSON>",
      role: "Student Visa Applicant",
      location: "Madrid, Spain",
      text: "Saved me time and hassle for my student visa. The ticket looked completely authentic! Embassy didn't question it at all.",
      color: "#8B5CF6",
      avatar: "ER",
      timeAgo: "3 days ago",
      visaType: "Student Visa",
      approved: true
    }
  ];

  const StarIcon = () => (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
    </svg>
  );

  return (
    <section className="section-rhythm bg-white relative overflow-hidden">
      {/* Premium aviation background elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-brand-400/8 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-0 left-0 w-80 h-80 bg-accent-400/8 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 opacity-[0.015]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>
      <div className="container-modern relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          {/* Premium social proof badge */}
          <div className="inline-flex items-center bg-white/90 backdrop-blur-md text-brand-700 px-8 py-4 rounded-2xl text-base font-bold mb-8 shadow-aviation border border-brand-200">
            <div className="w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center mr-3">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </div>
            75,000+ Successful Visa Applications Worldwide
          </div>

          <h2 className="text-5xl md:text-6xl font-black text-brand-800 mb-8 leading-tight">
            Real Success Stories from
            <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent block">
              Approved Travelers
            </span>
          </h2>

          {/* Premium stats grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-12">
            <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-aviation border border-brand-200">
              <div className="text-4xl font-black text-brand-600 mb-2">4.9/5</div>
              <div className="flex justify-center text-accent-500 mb-3">
                {[...Array(5)].map((_, i) => (
                  <StarIcon key={i} />
                ))}
              </div>
              <div className="text-sm font-bold text-brand-700">Customer Rating</div>
            </div>
            <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-aviation border border-accent-200">
              <div className="text-4xl font-black text-accent-600 mb-2">99.7%</div>
              <div className="text-sm font-bold text-accent-700 mt-4">Embassy Success Rate</div>
            </div>
            <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-aviation border border-green-200">
              <div className="text-4xl font-black text-green-600 mb-2">60s</div>
              <div className="text-sm font-bold text-green-700 mt-4">Average Delivery Time</div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              whileHover={{ y: -8, scale: 1.02 }}
              transition={{
                duration: 0.6,
                delay: index * 0.15,
                type: "spring",
                stiffness: 100
              }}
              className="premium-card group relative overflow-hidden p-8 cursor-pointer"
            >
              {/* Premium gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white to-neutral-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div className="relative z-10">
                {/* Header with approval badge */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center text-yellow-400">
                    {[...Array(5)].map((_, starIndex) => (
                      <StarIcon key={starIndex} />
                    ))}
                  </div>
                  {testimonial.approved && (
                    <div className="inline-flex items-center bg-accent-100 text-accent-700 px-2 py-1 rounded-full text-xs font-semibold">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      APPROVED
                    </div>
                  )}
                </div>

                {/* Testimonial Text */}
                <p className="text-neutral-700 italic mb-8 text-left leading-relaxed text-lg">
                  "{testimonial.text}"
                </p>

                {/* Customer Info */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <div
                      className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold mr-3 shadow-medium"
                      style={{ backgroundColor: testimonial.color }}
                    >
                      {testimonial.avatar}
                    </div>
                    <div>
                      <div className="font-bold text-neutral-800">{testimonial.name}</div>
                      <div className="text-sm text-neutral-600">{testimonial.location}</div>
                      <div className="text-xs text-brand-600 font-semibold">{testimonial.visaType}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-neutral-500">{testimonial.timeAgo}</div>
                  </div>
                </div>
              </div>

              {/* Premium shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            </motion.div>
          ))}
        </div>

        {/* Additional Social Proof */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm rounded-2xl px-8 py-4 shadow-soft border border-neutral-200">
            <div className="flex items-center space-x-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-neutral-800">847</div>
                <div className="text-xs text-neutral-600">Reservations Today</div>
              </div>
              <div className="w-px h-8 bg-neutral-300"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-neutral-800">23</div>
                <div className="text-xs text-neutral-600">Countries Served</div>
              </div>
              <div className="w-px h-8 bg-neutral-300"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-neutral-800">99.7%</div>
                <div className="text-xs text-neutral-600">Success Rate</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
