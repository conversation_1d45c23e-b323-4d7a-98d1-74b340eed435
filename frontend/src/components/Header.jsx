import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Header = () => {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <>

      {/* Main Header with Blue Gradient */}
      <header className="sticky top-0 z-50 bg-gradient-to-r from-blue-50 via-brand-50 to-blue-50 backdrop-blur-md border-b border-brand-100/30 shadow-luxury-soft">
        <div className="container-modern">
          <div className="flex justify-between items-center h-20">
          {/* Premium Aviation Logo */}
          <Link
            to="/"
            className="flex items-center space-x-3 group"
            aria-label="VerifiedOnward - Embassy Approved Flight Reservations - Go to homepage"
          >
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-brand-500 to-brand-600 rounded-2xl flex items-center justify-center group-hover:from-brand-600 group-hover:to-brand-700 transition-all duration-300 shadow-aviation group-hover:shadow-aviation-hover transform group-hover:scale-105">
                <svg
                  className="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                </svg>
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent-500 rounded-full flex items-center justify-center">
                <svg
                  className="w-2 h-2 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-black text-brand-700 group-hover:text-brand-800 transition-colors duration-300">VerifiedOnward</span>
              <span className="text-xs font-semibold text-accent-600 -mt-1">Embassy Approved</span>
            </div>
          </Link>

          {/* Premium Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">

            <Link
              to="/"
              className={`nav-luxury-item ${
                location.pathname === '/' ? 'active' : ''
              }`}
              aria-current={location.pathname === '/' ? 'page' : undefined}
            >
              Home
            </Link>
            <Link
              to="/how-it-works"
              className={`nav-luxury-item ${
                location.pathname === '/how-it-works' ? 'active' : ''
              }`}
            >
              How It Works
            </Link>

            <Link
              to="/faq"
              className={`nav-luxury-item ${
                location.pathname === '/faq' ? 'active' : ''
              }`}
            >
              FAQ
            </Link>


            {/* Enhanced CTA Button with Consistent Styling */}
            <Link
              to="/#search-form"
              className="btn-cta-primary btn-cta-small"
              onClick={(e) => {
                if (window.location.pathname === '/') {
                  e.preventDefault();
                  const searchForm = document.querySelector('#search-form');
                  if (searchForm) {
                    searchForm.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  }
                }
              }}
            >
              <span className="flex items-center space-x-2">
                <span>Get Started</span>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </span>
            </Link>
          </nav>

          {/* Premium Mobile menu button */}
          <div className="lg:hidden">
            <button
              className="p-3 rounded-2xl text-brand-600 hover:text-brand-700 hover:bg-brand-50 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transition-all duration-300 shadow-soft hover:shadow-aviation"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d={mobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Premium Mobile Navigation Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden border-t border-brand-100 bg-white/95 backdrop-blur-md shadow-aviation">
            <div className="px-6 pt-6 pb-8 space-y-4">
              {/* Mobile Trust Indicator */}
              <div className="flex items-center justify-center space-x-2 px-4 py-2 bg-brand-50 rounded-full border border-brand-200 mb-4">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-semibold text-brand-700">99.7% Embassy Success Rate</span>
              </div>

              <Link
                to="/"
                className={`block px-6 py-4 rounded-2xl text-lg font-bold transition-all duration-300 ${
                  location.pathname === '/'
                    ? 'text-brand-600 bg-brand-50 border-2 border-brand-200 shadow-aviation'
                    : 'text-neutral-700 hover:text-brand-600 hover:bg-brand-50 hover:shadow-soft'
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                to="/how-it-works"
                className={`block px-6 py-4 rounded-2xl text-lg font-bold transition-all duration-300 ${
                  location.pathname === '/how-it-works'
                    ? 'text-brand-600 bg-brand-50 border-2 border-brand-200 shadow-aviation'
                    : 'text-neutral-700 hover:text-brand-600 hover:bg-brand-50 hover:shadow-soft'
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                How It Works
              </Link>

              <Link
                to="/faq"
                className={`block px-6 py-4 rounded-2xl text-lg font-bold transition-all duration-300 ${
                  location.pathname === '/faq'
                    ? 'text-brand-600 bg-brand-50 border-2 border-brand-200 shadow-aviation'
                    : 'text-neutral-700 hover:text-brand-600 hover:bg-brand-50 hover:shadow-soft'
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                FAQ
              </Link>


              {/* Premium Mobile CTA */}
              <Link
                to="/#search-form"
                className="block px-6 py-4 mt-8 bg-gradient-to-r from-brand-500 to-brand-600 text-white rounded-2xl text-lg font-bold hover:from-brand-600 hover:to-brand-700 text-center shadow-aviation hover:shadow-aviation-hover transition-all duration-300 transform hover:scale-105"
                onClick={(e) => {
                  setMobileMenuOpen(false);
                  if (window.location.pathname === '/') {
                    e.preventDefault();
                    setTimeout(() => {
                      const searchForm = document.querySelector('#search-form');
                      if (searchForm) {
                        searchForm.scrollIntoView({ behavior: 'smooth', block: 'center' });
                      }
                    }, 100);
                  }
                }}
              >
                <span className="flex items-center justify-center space-x-2">
                  <span>Get Started</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </span>
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
    </>
  );
};

export default Header;
